import { swaggerSpec } from './swaggerConfig';

// Erweiterte OpenAPI-Spezifikation mit manuell definierten Endpunkten
export const openApiSpec = {
  ...swaggerSpec,
  paths: {
    '/api/v1/test/apikey': {
      get: {
        tags: ['Test'],
        summary: 'API-Key Validierung testen',
        description: 'Test-Endpunkt zur Validierung von API-Keys. Gibt Contact-Informationen bei gültigem Key zurück.',
        security: [{ ApiKeyAuth: [] }],
        responses: {
          '200': {
            description: 'API-Key ist gültig',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ApiKeyTestResponse',
                },
                example: {
                  message: 'API key is valid',
                  contact: {
                    id: 'contact_123',
                    name: 'Beispiel CPO',
                    cpo: true,
                    ou: {
                      id: 'ou_456',
                      name: 'Beispiel OU',
                    },
                  },
                  timestamp: '2024-06-24T10:30:00Z',
                },
              },
            },
          },
          '401': {
            description: 'API-Key ungültig oder fehlt',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
                example: {
                  error: 'API key validation failed',
                  message: 'Invalid or missing API key',
                },
              },
            },
          },
        },
      },
      post: {
        tags: ['Test'],
        summary: 'API-Key Validierung testen (POST)',
        description: 'Test-Endpunkt für POST-Requests mit API-Key-Validierung.',
        security: [{ ApiKeyAuth: [] }],
        requestBody: {
          description: 'Optionale Test-Daten',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  testData: {
                    type: 'string',
                    description: 'Test-Daten für POST-Request',
                  },
                },
              },
              example: {
                testData: 'Beispiel-Daten',
              },
            },
          },
        },
        responses: {
          '200': {
            description: 'POST-Request erfolgreich',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ApiKeyTestResponse',
                },
              },
            },
          },
          '401': {
            description: 'API-Key ungültig oder fehlt',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
        },
      },
    },
    '/api/v1/ou/charging-power': {
      get: {
        tags: ['Charging Power'],
        summary: 'Aktuelle Ladeleistung Zusammenfassung',
        description: 'Gibt eine Zusammenfassung der aktuellen Ladeleistung für eine OU zurück: Gesamtleistung in kW, Anzahl aktiver Sessions und heutige kWh-Summe aus CDR-Daten.',
        security: [{ ApiKeyAuth: [] }],
        responses: {
          '200': {
            description: 'Ladeleistungsdaten erfolgreich abgerufen',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ChargingPowerResponse',
                },
                example: {
                  success: true,
                  timestamp: '2024-06-24T10:30:00Z',
                  contact: {
                    name: 'Beispiel CPO',
                  },
                  chargingPower: {
                    totalCurrentKw: '45.60',
                    totalActiveSessions: 3,
                    todayTotalKwh: '234.80',
                  },
                  statistics: {
                    totalOusChecked: 5,
                    totalActiveSessions: 3,
                  },
                },
              },
            },
          },
          '400': {
            description: 'Fehlerhafte Anfrage',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
                example: {
                  error: 'No OU found for contact',
                  message: 'Contact ist keiner OU zugeordnet',
                },
              },
            },
          },
          '401': {
            description: 'API-Key ungültig oder fehlt',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
          '500': {
            description: 'Interner Serverfehler',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
                example: {
                  error: 'Internal server error',
                  message: 'Fehler beim Laden der Ladeleistungsdaten',
                },
              },
            },
          },
        },
      },
    },
    '/api/v1/contact/tokens': {
      post: {
        tags: ['Token Management'],
        summary: 'Tokens erstellen/aktualisieren',
        description: 'Erstellt oder aktualisiert Tokens für einen Contact. Die Tokens werden automatisch zu Longship synchronisiert.',
        security: [{ ApiKeyAuth: [] }],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/TokenArray',
              },
              example: {
                tokens: [
                  {
                    name: 'Mitarbeiter Token 1',
                    uid: 'TOKEN_UID_12345',
                    note: 'Token für Mitarbeiter Max Mustermann',
                  },
                  {
                    name: 'Mitarbeiter Token 2',
                    uid: 'TOKEN_UID_67890',
                    note: 'Token für Mitarbeiter Anna Schmidt',
                  },
                ],
              },
            },
          },
        },
        responses: {
          '200': {
            description: 'Tokens erfolgreich erstellt/aktualisiert',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    message: {
                      type: 'string',
                      description: 'Erfolgsmeldung',
                    },
                    createdTokens: {
                      type: 'integer',
                      description: 'Anzahl erstellter Tokens',
                    },
                    updatedTokens: {
                      type: 'integer',
                      description: 'Anzahl aktualisierter Tokens',
                    },
                    longshipSync: {
                      type: 'object',
                      properties: {
                        success: {
                          type: 'boolean',
                          description: 'Longship-Synchronisation erfolgreich',
                        },
                        syncedTokens: {
                          type: 'integer',
                          description: 'Anzahl synchronisierter Tokens',
                        },
                      },
                    },
                  },
                },
                example: {
                  message: 'Tokens erfolgreich verarbeitet',
                  createdTokens: 2,
                  updatedTokens: 0,
                  longshipSync: {
                    success: true,
                    syncedTokens: 2,
                  },
                },
              },
            },
          },
          '400': {
            description: 'Ungültige Eingabedaten',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
                example: {
                  error: 'Validation error',
                  message: 'Mindestens ein Token ist erforderlich',
                },
              },
            },
          },
          '401': {
            description: 'API-Key ungültig oder fehlt',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
          '500': {
            description: 'Interner Serverfehler',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
                example: {
                  error: 'Internal server error',
                  message: 'Fehler beim Verarbeiten der Tokens',
                },
              },
            },
          },
        },
      },
    },
    '/api/v1/iOSWidgets/charging-power': {
      get: {
        tags: ['iOS Widgets'],
        summary: 'iOS Widget .scriptable Datei herunterladen',
        description: 'Lädt eine .scriptable Datei für iOS Widgets herunter. Die Datei enthält ein vorkonfiguriertes Script mit dem API-Key des Benutzers.',
        security: [{ ApiKeyAuth: [] }],
        responses: {
          '200': {
            description: '.scriptable Datei erfolgreich generiert',
            content: {
              'application/octet-stream': {
                schema: {
                  type: 'string',
                  format: 'binary',
                  description: 'iOS .scriptable Datei',
                },
              },
            },
            headers: {
              'Content-Disposition': {
                description: 'Dateiname für Download',
                schema: {
                  type: 'string',
                  example: 'attachment; filename="EulWidget.scriptable"',
                },
              },
              'Content-Type': {
                description: 'MIME-Type für iOS-Erkennung',
                schema: {
                  type: 'string',
                  example: 'application/octet-stream',
                },
              },
            },
          },
          '401': {
            description: 'API-Key ungültig oder fehlt',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
              },
            },
          },
          '500': {
            description: 'Interner Serverfehler',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/Error',
                },
                example: {
                  error: 'Internal server error',
                  message: 'Fehler beim Generieren der .scriptable Datei',
                },
              },
            },
          },
        },
      },
    },
  },
};
