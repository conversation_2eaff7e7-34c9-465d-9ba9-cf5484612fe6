import swaggerJsdoc from "swagger-jsdoc";
import { env } from "~/env";

const options: swaggerJsdoc.Options = {
  definition: {
    openapi: "3.0.0",
    info: {
      title: "Eulektro API v1",
      version: "1.0.0",
      description: "API-Dokumentation für Eulektro v1 Endpunkte",
      contact: {
        name: "Eulektro Support",
        email: "<EMAIL>",
      },
    },
    servers: [
      {
        url: env.NEXT_PUBLIC_SITE_URL || "http://localhost:3000",
        description: "Eulektro API Server",
      },
    ],
    components: {
      securitySchemes: {
        ApiKeyAuth: {
          type: "apiKey",
          in: "header",
          name: "Authorization",
          description:
            'API Key für die Authentifizierung. Format: "Bearer YOUR_API_KEY" oder "ApiKey YOUR_API_KEY" oder direkt "YOUR_API_KEY"',
        },
      },
      schemas: {
        Error: {
          type: "object",
          properties: {
            error: {
              type: "string",
              description: "Fehlermeldung",
            },
            message: {
              type: "string",
              description: "Detaillierte Fehlerbeschreibung",
            },
          },
          required: ["error"],
        },
        Contact: {
          type: "object",
          properties: {
            id: {
              type: "string",
              description: "Eindeutige Contact-ID",
            },
            name: {
              type: "string",
              description: "Name des Contacts",
            },
            cpo: {
              type: "boolean",
              description: "Ist der Contact ein CPO",
            },
            ou: {
              type: "object",
              properties: {
                id: {
                  type: "string",
                  description: "OU-ID",
                },
                name: {
                  type: "string",
                  description: "OU-Name",
                },
              },
            },
          },
        },
        TokenObject: {
          type: "object",
          properties: {
            name: {
              type: "string",
              description: "Name des Tokens",
              minLength: 1,
            },
            uid: {
              type: "string",
              description: "Eindeutige Token-UID",
              minLength: 1,
            },
            note: {
              type: "string",
              description: "Optionale Notiz zum Token",
              default: "",
            },
          },
          required: ["name", "uid"],
        },
        TokenArray: {
          type: "object",
          properties: {
            tokens: {
              type: "array",
              items: {
                $ref: "#/components/schemas/TokenObject",
              },
              minItems: 1,
              description: "Array von Token-Objekten",
            },
          },
          required: ["tokens"],
        },
        ChargingPowerResponse: {
          type: "object",
          properties: {
            success: {
              type: "boolean",
              description: "Erfolg der Anfrage",
            },
            timestamp: {
              type: "string",
              format: "date-time",
              description: "Zeitstempel der Anfrage",
            },
            contact: {
              type: "object",
              properties: {
                name: {
                  type: "string",
                  description: "Name des Contacts",
                },
              },
            },
            chargingPower: {
              type: "object",
              properties: {
                totalCurrentKw: {
                  type: "string",
                  description:
                    "Gesamte aktuelle Ladeleistung in kW (als String mit 2 Dezimalstellen)",
                },
                totalActiveSessions: {
                  type: "integer",
                  description: "Anzahl aktiver Ladesessions",
                },
                todayTotalKwh: {
                  type: "string",
                  description: "Gesamte heutige Energie in kWh (als String mit 2 Dezimalstellen)",
                },
              },
            },
            statistics: {
              type: "object",
              properties: {
                totalOusChecked: {
                  type: "integer",
                  description: "Anzahl der überprüften OUs",
                },
                totalActiveSessions: {
                  type: "integer",
                  description:
                    "Anzahl aktiver Ladesessions (Duplikat von chargingPower.totalActiveSessions)",
                },
              },
            },
          },
        },
        ApiKeyTestResponse: {
          type: "object",
          properties: {
            message: {
              type: "string",
              description: "Erfolgsmeldung",
            },
            contact: {
              $ref: "#/components/schemas/Contact",
            },
            timestamp: {
              type: "string",
              format: "date-time",
              description: "Zeitstempel der Anfrage",
            },
          },
        },
      },
    },
    security: [
      {
        ApiKeyAuth: [],
      },
    ],
  },
  apis: [
    "./src/app/api/v1/**/*.ts", // Pfad zu den API-Routen
  ],
};

export const swaggerSpec = swaggerJsdoc(options);
