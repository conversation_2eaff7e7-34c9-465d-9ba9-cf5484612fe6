import { NextResponse } from "next/server";
import { openApiSpec } from "~/utils/swagger/openApiSpec";

/**
 * API-Endpunkt für die OpenAPI JSON-Spezifikation
 * Stellt die Swagger/OpenAPI-Spezifikation als JSON bereit
 * URL: GET /api/v1/docs/swagger.json
 */
export async function GET() {
  try {
    return NextResponse.json(openApiSpec, {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=3600', // Cache für 1 Stunde
      },
    });
  } catch (error) {
    console.error('Error generating OpenAPI spec:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: 'Fehler beim Generieren der OpenAPI-Spezifikation'
      },
      { status: 500 }
    );
  }
}
