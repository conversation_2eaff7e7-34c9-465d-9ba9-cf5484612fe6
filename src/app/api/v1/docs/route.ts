import { NextRequest, NextResponse } from "next/server";
import { readFileSync } from "fs";
import { join } from "path";

/**
 * Swagger UI für die API-Dokumentation
 * Stellt eine interaktive Swagger UI für die v1 API bereit
 * URL: GET /api/v1/docs
 */
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const baseUrl = `${url.protocol}//${url.host}`;

    // HTML für Swagger UI generieren
    const swaggerHtml = `
<!DOCTYPE html>
<html lang="de">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Eulektro API v1 - Dokumentation</title>
  <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@5.25.2/swagger-ui.css" />
  <style>
    html {
      box-sizing: border-box;
      overflow: -moz-scrollbars-vertical;
      overflow-y: scroll;
    }
    *, *:before, *:after {
      box-sizing: inherit;
    }
    body {
      margin:0;
      background: #fafafa;
      font-family: "Source Code Pro", Helvetica, Arial, sans-serif;
    }
    .swagger-ui .topbar {
      background-color: #21697C;
    }
    .swagger-ui .topbar .download-url-wrapper {
      display: none;
    }
    .swagger-ui .info .title {
      color: #21697C;
    }
    .swagger-ui .scheme-container {
      background: #fff;
      box-shadow: 0 1px 2px 0 rgba(0,0,0,0.1);
    }
    .custom-header {
      background:#21697C
      color: white;
      padding: 20px;
      text-align: center;
      margin-bottom: 20px;
    }
    .custom-header h1 {
      margin: 0;
      color: #21697C;
      font-size: 2.5em;
      font-weight: 500;
      font-family: "Montserrat", sans-serif;
    }
    .custom-header p {
      margin: 10px 0 0 0;
      font-size: 1.1em;
      opacity: 0.9;
    }
    .api-info {
      background: white;
      padding: 20px;
      margin: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .api-info h3 {
      color: #1f2937;
      margin-top: 0;
    }
    .api-info code {
      background: #f3f4f6;
      padding: 2px 6px;
      border-radius: 4px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    }
  </style>
</head>
<body>
  <div class="custom-header">
    <h1>🔌 Eulektro API v1</h1>
    <p>Interaktive API-Dokumentation für Eulektro v1 Endpunkte</p>
  </div>
  
  <div class="api-info">
    <h3>🔑 API-Key Authentifizierung</h3>
    <p>Alle API-Endpunkte erfordern einen gültigen API-Key im Authorization-Header:</p>
    <ul>
      <li><code>Authorization: Bearer YOUR_API_KEY</code></li>
      <li><code>Authorization: ApiKey YOUR_API_KEY</code></li>
      <li><code>Authorization: YOUR_API_KEY</code></li>
    </ul>
  </div>

  <div id="swagger-ui"></div>

  <script src="https://unpkg.com/swagger-ui-dist@5.25.2/swagger-ui-bundle.js"></script>
  <script src="https://unpkg.com/swagger-ui-dist@5.25.2/swagger-ui-standalone-preset.js"></script>
  <script>
    window.onload = function() {
      const ui = SwaggerUIBundle({
        url: '${baseUrl}/api/v1/docs/swagger.json',
        dom_id: '#swagger-ui',
        deepLinking: true,
        presets: [
          SwaggerUIBundle.presets.apis,
          SwaggerUIStandalonePreset
        ],
        plugins: [
          SwaggerUIBundle.plugins.DownloadUrl
        ],
        layout: "StandaloneLayout",
        defaultModelsExpandDepth: 1,
        defaultModelExpandDepth: 1,
        docExpansion: "list",
        filter: true,
        showExtensions: true,
        showCommonExtensions: true,
        tryItOutEnabled: true,
        requestInterceptor: function(request) {
          // Automatisch Content-Type für JSON-Requests setzen
          if (request.body && typeof request.body === 'object') {
            request.headers['Content-Type'] = 'application/json';
          }
          return request;
        },
        responseInterceptor: function(response) {
          // Response-Logging für Debugging
          console.log('API Response:', response);
          return response;
        },
        onComplete: function() {
          console.log('Swagger UI loaded successfully');
          
          // Custom styling nach dem Laden
          const style = document.createElement('style');
          style.textContent = \`
            .swagger-ui .topbar .download-url-wrapper {
              display: none !important;
            }
            .swagger-ui .info .title {
              font-size: 2em !important;
            }
            .swagger-ui .scheme-container {
              margin: 20px 0 !important;
              padding: 20px !important;
            }
          \`;
          document.head.appendChild(style);
        },
        validatorUrl: null, // Validator deaktivieren
        supportedSubmitMethods: ['get', 'post', 'put', 'delete', 'patch'],
        persistAuthorization: true, // API-Key zwischen Sitzungen speichern
      });

      // Custom Event Listener für bessere UX
      window.ui = ui;
    };
  </script>
</body>
</html>`;

    return new NextResponse(swaggerHtml, {
      headers: {
        "Content-Type": "text/html; charset=utf-8",
        "Cache-Control": "public, max-age=3600", // Cache für 1 Stunde
      },
    });
  } catch (error) {
    console.error("Error serving Swagger UI:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        message: "Fehler beim Laden der Swagger UI",
      },
      { status: 500 },
    );
  }
}
