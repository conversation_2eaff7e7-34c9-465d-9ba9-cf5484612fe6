import { NextRequest, NextResponse } from "next/server";
import { validateApi<PERSON><PERSON> } from "~/utils/apiAuth/apiKeyUtil";

/**
 * @swagger
 * /api/v1/test/apikey:
 *   get:
 *     tags:
 *       - Test
 *     summary: API-Key Validierung testen
 *     description: Test-Endpunkt zur Validierung von API-Keys. Gibt Contact-Informationen bei gültigem Key zurück.
 *     security:
 *       - ApiKeyAuth: []
 *     responses:
 *       200:
 *         description: API-Key ist gültig
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiKeyTestResponse'
 *       401:
 *         description: API-Key ungültig oder fehlt
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *
 * Test endpoint to demonstrate API key validation
 * This endpoint requires a valid API key in the Authorization header
 * URL: GET /api/v1/test/apikey
 */
export async function GET(request: NextRequest) {
  // Validate API key using utility function
  const validation = await validateApiKey(request);

  if (!validation.isValid) {
    return NextResponse.json(
      { 
        error: "API key validation failed", 
        message: validation.error 
      },
      { status: 401 }
    );
  }

  // API key is valid, return success response with contact info
  return NextResponse.json({
    success: true,
    message: "API key validation successful",
    timestamp: new Date().toISOString(),
    contact: {
      id: validation.contact?.id,
      name: validation.contact?.name,
      companyName: validation.contact?.companyName,
      cpo: validation.contact?.cpo,
      ou: validation.contact?.ou ? {
        id: validation.contact.ou.id,
        name: validation.contact.ou.name,
        code: validation.contact.ou.code,
      } : null,
    },
  });
}

/**
 * @swagger
 * /api/v1/test/apikey:
 *   post:
 *     tags:
 *       - Test
 *     summary: API-Key Validierung testen (POST)
 *     description: Test-Endpunkt für POST-Requests mit API-Key-Validierung.
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       description: Optionale Test-Daten
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               testData:
 *                 type: string
 *                 description: Test-Daten für POST-Request
 *     responses:
 *       200:
 *         description: POST-Request erfolgreich
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiKeyTestResponse'
 *       401:
 *         description: API-Key ungültig oder fehlt
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *
 * Test endpoint for POST requests with API key validation
 * URL: POST /api/v1/test/apikey
 */
export async function POST(request: NextRequest) {
  // Validate API key using utility function
  const validation = await validateApiKey(request);

  if (!validation.isValid) {
    return NextResponse.json(
      { 
        error: "API key validation failed", 
        message: validation.error 
      },
      { status: 401 }
    );
  }

  try {
    const body = await request.json();
    
    return NextResponse.json({
      success: true,
      message: "API key validation successful for POST request",
      timestamp: new Date().toISOString(),
      receivedData: body,
      contact: {
        id: validation.contact?.id,
        name: validation.contact?.name,
        companyName: validation.contact?.companyName,
        cpo: validation.contact?.cpo,
      },
    });
  } catch (error) {
    return NextResponse.json({
      success: true,
      message: "API key validation successful for POST request (no JSON body)",
      timestamp: new Date().toISOString(),
      contact: {
        id: validation.contact?.id,
        name: validation.contact?.name,
        companyName: validation.contact?.companyName,
        cpo: validation.contact?.cpo,
      },
    });
  }
}
