import { NextRequest, NextResponse } from "next/server";
import { validate<PERSON><PERSON><PERSON><PERSON> } from "~/utils/apiAuth/apiKeyUtil";
import { readFileSync } from "fs";
import { join } from "path";
export const dynamic = "force-dynamic";
/**
 * @swagger
 * /api/v1/iOSWidgets/charging-power:
 *   get:
 *     tags:
 *       - iOS Widgets
 *     summary: iOS Widget .scriptable Datei herunterladen
 *     description: Lädt eine .scriptable Datei für iOS Widgets herunter. Die Datei enthält ein vorkonfiguriertes Script mit dem API-Key des Benutzers.
 *     security:
 *       - ApiKeyAuth: []
 *     responses:
 *       200:
 *         description: .scriptable Datei erfolgreich generiert
 *         content:
 *           application/octet-stream:
 *             schema:
 *               type: string
 *               format: binary
 *               description: iOS .scriptable Datei
 *         headers:
 *           Content-Disposition:
 *             description: Dateiname für Download
 *             schema:
 *               type: string
 *               example: 'attachment; filename="EulWidget.scriptable"'
 *           Content-Type:
 *             description: MIME-Type für iOS-Erkennung
 *             schema:
 *               type: string
 *               example: application/octet-stream
 *       401:
 *         description: API-Key ungültig oder fehlt
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Interner Serverfehler
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *
 * API-Endpunkt für Download einer .scriptable Datei für iOS Widget
 * Erstellt eine Scriptable-Datei mit dem Eulektro Charging Power Widget
 * und ersetzt den API-Key Platzhalter mit dem echten API-Key aus dem Request
 * URL: GET /api/v1/iOSWidgets/charging-power
 */
export async function GET(request: NextRequest) {
  try {
    // API-Key Validierung
    const validation = await validateApiKey(request);

    if (!validation.isValid) {
      return NextResponse.json(
        {
          error: "API key validation failed",
          message: validation.error,
        },
        { status: 401 },
      );
    }

    // API-Key aus dem Request extrahieren
    let authHeader = request.headers.get("authorization");
    if (!authHeader) {
      authHeader = request.headers.get("Authorization");
    }
    if (!authHeader) {
      authHeader = request.headers.get("AUTHORIZATION");
    }

    let apiKey: string;
    if (authHeader?.startsWith("Bearer ")) {
      apiKey = authHeader.replace("Bearer ", "");
    } else if (authHeader?.startsWith("ApiKey ")) {
      apiKey = authHeader.replace("ApiKey ", "");
    } else {
      apiKey = authHeader || "";
    }

    // Template-Datei laden
    const templatePath = join(
      process.cwd(),
      "src/app/api/v1/iOSWidgets/templates/EulWidget.scriptable",
    );
    let fileContent = readFileSync(templatePath, "utf8");

    // API-Key im Template ersetzen
    fileContent = fileContent.replace(/XXXXAPIKEY/g, apiKey);

    // Response mit Download-Headers erstellen
    const response = new NextResponse(fileContent);

    response.headers.set("Content-Type", "application/octet-stream");
    response.headers.set("Content-Disposition", 'attachment; filename="EulWidget.scriptable"');
    response.headers.set("Cache-Control", "no-cache");
    response.headers.set("Content-Length", fileContent.length.toString());

    return response;
  } catch (error) {
    console.error("Error generating scriptable file:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        message: "Fehler beim Erstellen der Scriptable-Datei",
      },
      { status: 500 },
    );
  }
}
